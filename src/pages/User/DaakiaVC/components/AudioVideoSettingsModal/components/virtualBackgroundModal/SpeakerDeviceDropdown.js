import React from 'react';
import { Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';

/**
 * SpeakerDeviceDropdown Component
 * Reusable dropdown for speaker device selection
 * @param {Array} devices - Array of available devices
 * @param {string} selectedDeviceId - Currently selected device ID
 * @param {function} onDeviceChange - Device change handler
 * @param {boolean} hasPermission - Whether permission is granted
 * @param {string} permissionMessage - Message to show when no permission
 * @param {string} noDevicesMessage - Message to show when no devices found
 * @param {string} defaultDeviceName - Default device name
 * @param {string} selectPlaceholder - Placeholder text for selection
 * @param {boolean} disabled - Whether the dropdown is disabled
 */
function SpeakerDeviceDropdown({
  devices = [],
  selectedDeviceId,
  onDeviceChange,
  hasPermission = true, // Speakers don't require permissions like mic/camera
  permissionMessage = 'Grant permission to see devices',
  noDevicesMessage = 'No speaker devices found',
  defaultDeviceName = 'Default Speaker',
  selectPlaceholder = 'Select speaker',
  disabled = false
}) {

  const truncateDeviceName = (name) => {
    return name; // Return full name without truncation
  };

  // Create dropdown items
  const createDropdownItems = () => {
    if (!hasPermission) {
      return [{
        key: 'no-permission',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">{permissionMessage}</span>
          </div>
        ),
        disabled: true
      }];
    }

    if (devices.length === 0) {
      return [{
        key: 'no-devices',
        label: (
          <div className="device-dropdown-item disabled">
            <span className="device-name">{noDevicesMessage}</span>
          </div>
        ),
        disabled: true
      }];
    }

    return devices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`device-dropdown-item ${device.deviceId === selectedDeviceId ? 'selected' : ''}`}
          onClick={() => onDeviceChange(device.deviceId)}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || defaultDeviceName)}
          </span>
        </div>
      ),
    }));
  };

  // Get selected device name
  const getSelectedDeviceName = () => {
    if (!hasPermission) {
      return permissionMessage;
    }
    if (devices.length === 0) {
      return noDevicesMessage;
    }
    const selectedDevice = devices.find(device => device.deviceId === selectedDeviceId);
    return truncateDeviceName(selectedDevice?.label || selectPlaceholder);
  };

  return (
    <Dropdown
      menu={{ items: createDropdownItems() }}
      trigger={['click']}
      placement="bottomLeft"
      overlayClassName="device-settings-dropdown"
      disabled={disabled || !hasPermission || devices.length === 0}
    >
      <div className="custom-device-select">
        <span className="device-select-text">
          {getSelectedDeviceName()}
        </span>
        <DownOutlined className="dropdown-arrow" />
      </div>
    </Dropdown>
  );
}

export default SpeakerDeviceDropdown;