import React from 'react';
import TestButton from './TestButton';
import MicLevelIndicator from './MicLevelIndicator';
import SpeakerDeviceDropdown from '../virtualBackgroundModal/SpeakerDeviceDropdown';

/**
 * SpeakerSettings Component
 * Complete speaker configuration section
 * @param {Array} speakerDevices - Available speaker output devices
 * @param {string} speakerDeviceId - Selected speaker device ID
 * @param {function} onSpeakerDeviceChange - Speaker device change handler
 * @param {object} permissions - Speaker permission status
 * @param {function} onTestSpeaker - Speaker test handler
 * @param {boolean} isSpeakerTesting - Whether speaker is being tested
 * @param {number} speakerAudioLevel - Current speaker audio level (0-100)
 */
function SpeakerSettings({
  speakerDevices = [],
  speakerDeviceId,
  onSpeakerDeviceChange,
  permissions = { microphone: false },
  onTestSpeaker,
  isSpeakerTesting = false,
  speakerAudioLevel = 0
}) {
  // Determine button text and state
  const getTestButtonText = () => {
    return isSpeakerTesting ? 'Stop Test' : 'Test Speaker';
  };

  return (
    <div className="settings-section speaker-settings-section">
      <div className="grid-container speaker-grid">
        <div className="grid-cell left">
          <span className="setting-label">Speaker</span>
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Choose Speaker</span>
        </div>
        <div className="grid-cell center">
          <SpeakerDeviceDropdown
            devices={speakerDevices}
            selectedDeviceId={speakerDeviceId}
            onDeviceChange={onSpeakerDeviceChange}
            hasPermission={permissions.microphone}
            permissionMessage="Grant microphone permission to see devices"
            noDevicesMessage="No speaker devices found"
            defaultDeviceName="Default Speaker"
            selectPlaceholder="Select speaker"
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Test Speaker</span>
        </div>
        <div className="grid-cell left">
          <TestButton
            onClick={onTestSpeaker}
            isActive={isSpeakerTesting}
            activeText="Stop Test"
            inactiveText={getTestButtonText()}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Level:</span>
        </div>
        <div className="grid-cell center">
          <MicLevelIndicator
            level={speakerAudioLevel}
            isActive={isSpeakerTesting && speakerAudioLevel > 0}
          />
        </div>
      </div>
    </div>
  );
}

export default SpeakerSettings;
