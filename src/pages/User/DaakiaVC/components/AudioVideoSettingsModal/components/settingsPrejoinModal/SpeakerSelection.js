import React from 'react';
import DeviceDropdown from './DeviceDropdown';

/**
 * SpeakerSelection Component
 * Section for choosing a speaker device
 * @param {Array} speakerDevices - Available audio output devices
 * @param {string} speakerDeviceId - Selected speaker device ID
 * @param {function} onSpeakerDeviceChange - Speaker device change handler
 * @param {object} permissions - Microphone permission status
 */
function SpeakerSelection({
  speakerDevices,
  speakerDeviceId,
  onSpeakerDeviceChange,
  permissions
}) {
  return (
    <>
      <div className="grid-cell left">
        <span className="setting-label">Speaker</span>
      </div>
      <div className="grid-cell left">
        <span className="setting-sublabel">Choose Speaker</span>
      </div>
      <div className="grid-cell center">
        <DeviceDropdown
          devices={speakerDevices}
          selectedDeviceId={speakerDeviceId}
          onDeviceChange={onSpeakerDeviceChange}
          hasPermission={permissions.microphone}
          permissionMessage="Grant microphone permission to see devices"
          noDevicesMessage="No speaker devices found"
          defaultDeviceName="Default Speaker"
          selectPlaceholder="Select speaker"
        />
      </div>
      <div className="grid-cell center">{/* Empty cell */}</div>
    </>
  );
}

export default SpeakerSelection; 