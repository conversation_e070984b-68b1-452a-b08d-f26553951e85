import React from 'react';

/**
 * SpeakerLevelIndicator Component
 * Displays speaker audio level with rectangular bars
 * @param {number} level - Speaker audio level (0-100)
 * @param {boolean} isActive - Whether the indicator should show active state
 */
function SpeakerLevelIndicator({ level, isActive = false }) {
  const numberOfBars = 7;
  const activeBars = Math.ceil((level / 100) * numberOfBars);

  return (
    <div className="speaker-level-indicator">
      {Array.from({ length: numberOfBars }, (_, index) => {
        const isBarActive = isActive && index < activeBars;

        return (
          <div
            key={index}
            className={`speaker-bar ${isBarActive ? 'active' : ''}`}
            style={{
              height: '24px', // Same height for all bars (rectangular)
              flex: 1,  // Take equal share of available width
              backgroundColor: isBarActive ? '#3B60E4' : '#e8e8e8',
              borderRadius: '2px',
              transition: 'background-color 0.1s ease-in-out'
            }}
          />
        );
      })}
    </div>
  );
}

export default SpeakerLevelIndicator; 